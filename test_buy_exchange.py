

import json
import logging
import time
from exchange import BinanceFuturesAPI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


if __name__ == "__main__":
    api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
    secret_key = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

    # 初始化API客户端
    print("\n1. 初始化API客户端...")
    futures_api = BinanceFuturesAPI(api_key, secret_key)
    print("✅ API客户端初始化成功")

    """
    # 检查API权限
    print("\n2. 检查API权限...")
    account_info = futures_api.check_api_permissions()
    print(f"✅ 账户总资产: {account_info.get('totalWalletBalance', 'N/A')} USDT")
    """

    # 设置参数
    symbol = 'IPUSDC'   # 合约
    usdc_amount = 5.2   # 下单金额 usdc
    margin_type = 'CROSSED'  # 仓位模式
    leverage = 2        # 杠杆倍率
    offset_percent = 5  # 价格滑点

    print(f"\n3. 测试参数:")
    print(f"   交易对: {symbol}")
    print(f"   USDC金额: {usdc_amount}")
    print(f"   杠杆: {leverage}x")
    print(f"   仓位模式: {margin_type}")

    """
    # 检查是否已有持仓
    print(f"\n4. 检查 {symbol} 持仓状态...")
    has_position = futures_api._check_position_exists(symbol)
    if has_position:
        print(f"   {symbol} 已有持仓")
    else:
        print(f"   {symbol} 暂无持仓")
    """

    """
    # 测试仓位模式设置（仅在有持仓时）
    print(f"\n5. 仓位模式设置...")
    if has_position:
        try:
            futures_api.set_margin_type(symbol, margin_type)
            print(f"✅ 成功设置 {symbol} 仓位模式为 {margin_type}")
        except Exception as e:
            print(f"❌ 仓位模式设置失败: {e}")
            print("   这可能是因为该交易对不支持此操作或已经是目标模式")
    else:
        print("ℹ️  跳过仓位模式设置（无持仓）")
   """

    # 设置杠杆倍率
    print(f"\n6. 设置 {symbol} 杠杆倍率为 {leverage}x...")
    futures_api.set_leverage(symbol, leverage)
    print(f"   成功设置 {symbol} 杠杆倍率为 {leverage}x")

    # 获取订单信息
    while True:
        print(f"\n7. 获取 {symbol} 订单信息...")
        try:
            order_book = futures_api.get_order_book(symbol, limit=10)
            if order_book.get('bids') and order_book.get('asks'):
                bid_price = float(order_book['bids'][0][0])
                ask_price = float(order_book['asks'][0][0])
                print(
                    f"✅ 买一价: {bid_price}, 卖一价: {ask_price}, 设置标记价格为买一价...")
            else:
                print("❌ 获取订单信息异常")
                raise ValueError("获取订单信息异常")
        except Exception as e:
            print(f"❌ 获取订单信息失败: {e}")
            raise ValueError(f"获订单信息失败: {e}")

        # 执行下单
        print(f"\n8. 执行下单...")
        response = futures_api._place_order(
            symbol, 'BUY', 0, usdc_amount, bid_price, margin_type, leverage, 'usdc', offset_percent)
        result = response.json()

        if response.status_code == 400:
            result_code = result.get('code')
            if result_code == -2021:
                print("❌ 订单会立即成交并成为taker，无法挂单")
                print("开始拉取最新价重试...")
                time.sleep(1)
                continue
            else:
                error_code = result.get('code')
                error_msg = result.get('msg', '未知错误')

                detailed_msg = f"下单失败：{error_msg} (错误代码: {error_code})"
                logger.error(
                    f"下单失败: HTTP 400,无法解析错误响应: {response.text}, 错误信息: {detailed_msg}")
                break

        elif response.status_code == 200:
            print("🎉 下单成功！")
            print(f"   订单ID: {result.get('orderId')}")
            print(f"   订单状态: {result.get('status')}")
            print(f"   成交数量: {result.get('executedQty', 0)}")
            print(f"   委托数量: {result.get('origQty')}")
            print(f"   委托价格: {result.get('price')}")
            break

        else:
            logger.error(
                f"下单失败: 无法解析错误响应: {response.text}")
            break
