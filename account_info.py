import json
import requests
import time
import hmac
import hashlib
import urllib.parse
import logging
from typing import Dict, Optional, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BinanceAccountAPI:
    """
    Binance账户信息API封装类
    用于获取Binance现货账户信息
    """

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化Binance API客户端

        Args:
            api_key (str): Binance API密钥
            secret_key (str): Binance API密钥对应的私钥

        Raises:
            ConnectionError: 当无法连接到币安服务器时抛出异常
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = "https://api.binance.com"

        # 测试服务器连通性
        self.ping_server()

        logger.info("BinanceAccountAPI 初始化完成")

    def ping_server(self) -> None:
        """
        测试币安服务器连通性
        调用币安PING接口 /api/v3/ping 来测试服务器连通性

        Raises:
            ConnectionError: 当无法连接到币安服务器时抛出异常
        """
        logger.info("开始测试币安服务器连通性")

        try:
            # 发送PING请求到币安服务器
            response = requests.get(
                f'{self.base_url}/api/v3/ping',
                timeout=10  # 设置超时时间
            )

            # 检查响应状态
            response.raise_for_status()

            logger.info("币安服务器连通性测试成功")

        except requests.exceptions.RequestException as e:
            error_msg = f"无法连接到币安服务器: {e}"
            logger.error(error_msg)
            raise ConnectionError(error_msg) from e
        except Exception as e:
            error_msg = f"服务器连通性测试时发生未知错误: {e}"
            logger.error(error_msg)
            raise ConnectionError(error_msg) from e

    def generate_signature(self, query_string: str) -> str:
        """
        生成Binance API请求签名

        Args:
            query_string (str): 查询参数字符串

        Returns:
            str: HMAC SHA256签名
        """
        logger.info("开始生成API签名")
        try:
            # 使用HMAC SHA256算法生成签名
            signature = hmac.new(
                self.secret_key.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            logger.info("API签名生成成功")
            return signature

        except Exception as e:
            logger.error(f"生成签名时发生错误: {e}")
            raise

    def get_account_info(self, additional_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取Binance账户信息

        Args:
            additional_params (dict, optional): 额外的查询参数

        Returns:
            dict: 账户信息JSON数据
        """
        logger.info("开始获取账户信息")

        try:
            # 准备基础参数
            timestamp = int(time.time() * 1000)
            params: Dict[str, Any] = {'timestamp': timestamp}

            # 添加额外参数（如果有）
            if additional_params:
                params.update(additional_params)
                logger.info(f"添加额外参数: {additional_params}")

            # 生成查询字符串
            query_string = urllib.parse.urlencode(params)
            logger.info(f"查询字符串: {query_string}")

            # 生成签名
            signature = self.generate_signature(query_string)

            # 添加签名到参数
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送请求
            logger.info("发送API请求到Binance服务器")
            response = requests.get(
                f'{self.base_url}/api/v3/account',
                headers=headers,
                params=params,
                timeout=10  # 设置超时时间
            )

            # 检查响应状态
            response.raise_for_status()

            # 解析JSON响应
            account_data = response.json()
            logger.info("成功获取账户信息")

            # 过滤掉余额为0的资产
            if 'balances' in account_data:
                original_count = len(account_data['balances'])
                account_data['balances'] = [
                    balance for balance in account_data['balances']
                    if float(balance['free']) > 0 or float(balance['locked']) > 0
                ]
                filtered_count = len(account_data['balances'])
                logger.info(
                    f"过滤余额为0的资产: 原始数量 {original_count}, 过滤后数量 {filtered_count}")

            return account_data

        except requests.exceptions.RequestException as e:
            logger.error(f"请求发生错误: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取账户信息时发生未知错误: {e}")
            raise

    def execute_account_query(self) -> Dict[str, Any]:
        """
        执行完整的账户查询流程
        先生成签名，再获取账户信息

        Returns:
            dict: 账户信息JSON数据
        """
        logger.info("=" * 50)
        logger.info("开始执行账户查询流程")

        try:
            # 调用获取账户信息方法（内部会自动调用签名生成方法）
            account_info = self.get_account_info()

            logger.info("账户查询流程执行成功")
            logger.info("=" * 50)

            return account_info

        except Exception as e:
            logger.error(f"账户查询流程执行失败: {e}")
            logger.info("=" * 50)
            raise

    def query_account_balances(self) -> Dict[str, Any]:
        """
        运行账户信息查询演示
        封装了原main()函数的功能
        """
        try:
            # 执行账户查询
            account_data = self.execute_account_query()

            account_json = json.dumps(
                account_data, indent=2, ensure_ascii=False)
            # 格式化输出结果
            print("\n" + "=" * 60)
            print("账户信息查询结果:")
            print("=" * 60)
            print(account_json)

            return account_data

        except Exception as e:
            logger.error(f"程序执行失败: {e}")
            print(f"错误: {e}")
            raise

if __name__ == "__main__":
    api_key = "Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW"
    secret_key = "eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh"
    binance_api = BinanceAccountAPI(api_key, secret_key)
    binance_api.query_account_balances()
