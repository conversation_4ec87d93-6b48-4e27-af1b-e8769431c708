# 配置日志
import logging

from exchange import BinanceFuturesAPI


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    import time
    import urllib.parse
    import requests
    
    api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
    secret_key = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

    # 初始化API客户端
    print("\n1. 初始化API客户端...")
    futures_api = BinanceFuturesAPI(api_key, secret_key)
    print("✅ API客户端初始化成功")
    
    # 查询所有持仓信息
    positions = futures_api.query_position_info()
    
    
    
    
