# 币安期货合约下单数量类型设置说明

## 📋 概述

现在的币安期货下单系统支持两种数量类型：
1. **USDC金额** (`amount_type='usdc'`) - 系统自动计算对应的币数量
2. **币数量** (`amount_type='coin'`) - 直接指定币的数量

## 🔧 使用方法

### 方式1：使用USDC金额下单（推荐）

```python
from exchange import BinanceFuturesAPI

futures_api = BinanceFuturesAPI(api_key, secret_key)

# 使用10 USDC开多单
result = futures_api.place_long_order_with_price(
    symbol="IPUSDC",
    amount=10.0,                    # 10 USDC
    price=4.74,
    margin_type="CROSSED",
    leverage=2,
    amount_type='usdc'              # 指定使用USDC金额
)
```

**优势：**
- 直观易懂，知道投入多少资金
- 系统自动处理精度转换
- 自动满足最小名义价值要求

### 方式2：使用币数量下单

```python
# 使用2个币开多单
result = futures_api.place_long_order_with_price(
    symbol="IPUSDC",
    amount=2.0,                     # 2个币
    price=4.74,
    margin_type="CROSSED", 
    leverage=2,
    amount_type='coin'              # 指定使用币数量
)
```

**优势：**
- 精确控制币的数量
- 适合有特定数量需求的场景

### 方式3：默认方式（向后兼容）

```python
# 不指定amount_type，默认使用USDC金额
result = futures_api.place_long_order_with_price(
    symbol="IPUSDC",
    amount=10.0,                    # 默认为USDC金额
    price=4.74,
    margin_type="CROSSED",
    leverage=2
    # amount_type='usdc'           # 可以省略，默认为'usdc'
)
```

## 📊 精度处理机制

### 自动精度获取
系统通过币安API自动获取每个交易对的精度信息：

```python
# 系统自动获取的信息
- 数量精度：如IPUSDC为1位小数，BTCUSDT为3位小数
- 最小名义价值：如IPUSDC为5 USDT，BTCUSDT为100 USDT
- 价格精度：最小价格变动单位
```

### 精度处理示例

| 交易对 | 数量精度 | 最小名义价值 | 示例转换 |
|--------|----------|--------------|----------|
| IPUSDC | 1位小数 | 5.0 USDT | 10 USDC @ 4.74 = 2.1 币 |
| BTCUSDT | 3位小数 | 100.0 USDT | 50 USDC @ 50000 = 0.002 币 |
| ETHUSDT | 3位小数 | 20.0 USDT | 30 USDC @ 3000 = 0.01 币 |
| BNBUSDT | 2位小数 | 5.0 USDT | 10 USDC @ 4.75 = 2.11 币 |

## 🎯 适用的下单函数

所有下单函数都支持`amount_type`参数：

### 开多单函数
```python
# 指定价格
place_long_order_with_price(symbol, amount, price, margin_type, leverage, amount_type='usdc')

# BBO同向价1
place_long_order_with_bbo1(symbol, amount, margin_type, leverage, amount_type='usdc')

# BBO同向价5  
place_long_order_with_bbo5(symbol, amount, margin_type, leverage, amount_type='usdc')
```

### 开空单函数
```python
# 指定价格
place_short_order_with_price(symbol, amount, price, margin_type, leverage, amount_type='usdc')

# BBO同向价1
place_short_order_with_bbo1(symbol, amount, margin_type, leverage, amount_type='usdc')

# BBO同向价5
place_short_order_with_bbo5(symbol, amount, margin_type, leverage, amount_type='usdc')
```

## ⚠️ 注意事项

### 1. 参数验证
- `amount_type` 只能是 `'usdc'` 或 `'coin'`
- 数量必须大于0
- 价格必须大于0

### 2. 最小名义价值
- 系统自动确保订单满足交易所的最小名义价值要求
- 如果计算出的数量不满足要求，会自动调整

### 3. 精度处理
- 使用API获取的真实精度信息
- 如果API调用失败，会使用默认精度（3位小数）

### 4. 错误处理
```python
try:
    result = futures_api.place_long_order_with_price(
        symbol="IPUSDC",
        amount=10.0,
        price=4.74,
        margin_type="CROSSED",
        leverage=2,
        amount_type='usdc'
    )
    print(f"下单成功，订单ID: {result['orderId']}")
except ValueError as e:
    print(f"参数错误: {e}")
except Exception as e:
    print(f"下单失败: {e}")
```

## 📈 实际测试结果

### USDC金额方式测试
```
输入: 10.0 USDC @ 4.74 价格
输出: 2.1 币 (名义价值: 9.9540)
订单ID: 400645562
```

### 新旧方式对比
```
IPUSDC (10 USDC @ 4.75):
- 新方式 (API精度): 2.1 币, 名义价值: 9.9750
- 旧方式 (固定精度): 2.105 币, 名义价值: 9.9987
- 差异: 数量差 0.005, 名义价值差 0.0237

BTCUSDT (50 USDC @ 50000):
- 新方式 (API精度): 0.002 币, 名义价值: 100.0000
- 旧方式 (固定精度): 0.001 币, 名义价值: 50.0000  
- 差异: 数量差 0.001, 名义价值差 50.0000
```

## 🎉 总结

1. **推荐使用USDC金额方式** - 更直观，自动处理精度
2. **系统自动获取精度** - 确保符合交易所规则
3. **向后兼容** - 不指定`amount_type`默认使用USDC金额
4. **智能调整** - 自动满足最小名义价值要求
5. **错误处理** - 完善的异常处理机制

现在您可以根据需要选择最适合的数量类型进行下单！
